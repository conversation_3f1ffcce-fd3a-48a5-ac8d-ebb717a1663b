import React, { Fragment, useEffect } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { useForm, useFieldArray } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useQueryClient } from '@tanstack/react-query'
import { CommandTemplate, CreateCommandTemplateDTO, UpdateCommandTemplateDTO } from '../types/server'
import { createCommandTemplate, updateCommandTemplate } from '../services/api'
import { BookTemplate, FileText, Terminal, Plus, Trash2, X } from 'lucide-react'
import { CommandInput } from './CommandInput'
import { ScriptFileUpload } from './ScriptFileUpload'

interface TemplateModalProps {
  isOpen: boolean
  onClose: () => void
  mode: 'create' | 'edit'
  template?: CommandTemplate
  initialCommands?: {
    name: string
    command: string
    description?: string
    order: number
  }[]
}

const templateSchema = z.object({
  name: z.string().min(1, 'O nome é obrigatório'),
  description: z.string().optional(),
  commands: z.array(z.object({
    id: z.string().optional(),
    name: z.string().min(1, 'O nome é obrigatório'),
    command: z.string().min(1, 'O comando é obrigatório'),
    description: z.string().optional(),
    order: z.number().default(0),
  })).min(1, 'Adicione pelo menos um comando'),
})

type TemplateFormData = z.infer<typeof templateSchema>

export default function TemplateModal({ isOpen, onClose, mode, template, initialCommands }: TemplateModalProps) {
  const queryClient = useQueryClient()
  
  const { 
    register, 
    handleSubmit, 
    control, 
    reset, 
    formState: { errors, isSubmitting } 
  } = useForm<TemplateFormData>({
    resolver: zodResolver(templateSchema),
    defaultValues: {
      name: '',
      description: '',
      commands: [{ name: '', command: '', description: '', order: 0 }],
    },
  })

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'commands',
  })

  useEffect(() => {
    if (mode === 'edit' && template) {
      reset({
        name: template.name,
        description: template.description || '',
        commands: template.commands.map((cmd, index) => ({
          id: cmd.id,
          name: cmd.name,
          command: cmd.command,
          description: cmd.description || '',
          order: cmd.order || index,
        })),
      })
    } else if (mode === 'create' && initialCommands && initialCommands.length > 0) {
      reset({
        name: '',
        description: '',
        commands: initialCommands.map((cmd, index) => ({
          name: cmd.name,
          command: cmd.command,
          description: cmd.description || '',
          order: index,
        })),
      })
    }
  }, [mode, template, initialCommands, reset])

  async function onSubmit(data: TemplateFormData) {
    try {
      // Garantir que os comandos tenham uma ordem
      const commandsWithOrder = data.commands.map((cmd, index) => ({
        ...cmd,
        order: index,
      }))

      const formData = {
        ...data,
        commands: commandsWithOrder,
        // Definir isPublic como false por padrão, já que não é mais uma opção no formulário
        isPublic: false
      }

      if (mode === 'create') {
        await createCommandTemplate(formData as CreateCommandTemplateDTO)
      } else if (mode === 'edit' && template) {
        await updateCommandTemplate(template.id, formData as UpdateCommandTemplateDTO)
      }

      queryClient.invalidateQueries({ queryKey: ['commandTemplates'] })
      onClose()
      reset()
    } catch (error) {
      console.error('Erro ao salvar template:', error)
    }
  }

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-10" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-3xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <Dialog.Title as="div" className="flex justify-between items-center mb-4">
                  <div className="flex items-center gap-2">
                    <BookTemplate className="h-6 w-6 text-primary-500" />
                    <h3 className="text-lg font-medium text-gray-900">
                      {mode === 'create' ? 'Criar Novo Template' : 'Editar Template'}
                    </h3>
                  </div>
                  <button
                    onClick={onClose}
                    className="text-gray-400 hover:text-gray-500"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </Dialog.Title>

                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                        Nome do Template
                      </label>
                      <div className="mt-1 relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <BookTemplate className="h-5 w-5 text-gray-400" />
                        </div>
                        <input
                          type="text"
                          id="name"
                          className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                          placeholder="Ex: Comandos de Monitoramento"
                          {...register('name')}
                        />
                      </div>
                      {errors.name && (
                        <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                        Descrição (opcional)
                      </label>
                      <div className="mt-1 relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <FileText className="h-5 w-5 text-gray-400" />
                        </div>
                        <textarea
                          id="description"
                          rows={2}
                          className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                          placeholder="Descreva o propósito deste template"
                          {...register('description')}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="border-t border-gray-200 pt-4">
                    <div className="flex justify-between items-center mb-3">
                      <h4 className="text-md font-medium text-gray-700 flex items-center gap-1">
                        <Terminal className="h-5 w-5 text-gray-500" />
                        Comandos
                      </h4>
                      <button
                        type="button"
                        onClick={() => append({ name: '', command: '', description: '', order: fields.length })}
                        className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        Adicionar Comando
                      </button>
                    </div>

                    {errors.commands && !Array.isArray(errors.commands) && (
                      <p className="mt-1 text-sm text-red-600">{errors.commands.message}</p>
                    )}

                    <div className="space-y-4 mt-3">
                      {fields.map((field, index) => (
                        <div key={field.id} className="p-4 border border-gray-200 rounded-md bg-gray-50">
                          <div className="flex justify-between items-start mb-3">
                            <h5 className="text-sm font-medium text-gray-700 flex items-center gap-1">
                              <Terminal className="h-4 w-4 text-gray-500" />
                              Comando #{index + 1}
                            </h5>
                            {fields.length > 1 && (
                              <button
                                type="button"
                                onClick={() => remove(index)}
                                className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                              >
                                <Trash2 className="h-3.5 w-3.5 mr-1" />
                                Remover
                              </button>
                            )}
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                            <div>
                              <label className="block text-sm font-medium text-gray-700">
                                Nome
                              </label>
                              <div className="mt-1 relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                  <Terminal className="h-5 w-5 text-gray-400" />
                                </div>
                                <input
                                  type="text"
                                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                  placeholder="Ex: Status do Sistema"
                                  {...register(`commands.${index}.name`)}
                                />
                              </div>
                              {errors.commands?.[index]?.name && (
                                <p className="mt-1 text-sm text-red-600">
                                  {errors.commands[index]?.name?.message}
                                </p>
                              )}
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700">
                                Descrição (opcional)
                              </label>
                              <div className="mt-1 relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                  <FileText className="h-5 w-5 text-gray-400" />
                                </div>
                                <input
                                  type="text"
                                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                  placeholder="Ex: Exibe informações do sistema"
                                  {...register(`commands.${index}.description`)}
                                />
                              </div>
                            </div>
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Comando
                            </label>
                            <div className="space-y-2">
                              <CommandInput
                                name={`commands.${index}.command`}
                                register={register}
                                placeholder="Digite ou cole seus comandos aqui (suporte a multi-linhas)"
                                error={errors.commands?.[index]?.command?.message}
                                defaultValue={fields[index].command || ''}
                              />
                              <div className="flex justify-start">
                                <ScriptFileUpload
                                  onFileContent={(content) => {
                                    // Atualiza o valor no formulário
                                    const field = register(`commands.${index}.command`)
                                    field.onChange({
                                      target: {
                                        name: `commands.${index}.command`,
                                        value: content
                                      }
                                    } as any)

                                    // Atualiza diretamente o textarea para visualização imediata
                                    const textarea = document.querySelector(`textarea[name="commands.${index}.command"]`) as HTMLTextAreaElement
                                    if (textarea) {
                                      textarea.value = content
                                      // Dispara evento de input para acionar o auto-resize
                                      textarea.dispatchEvent(new Event('input', { bubbles: true }))
                                    }
                                  }}
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
                    <button
                      type="button"
                      onClick={onClose}
                      className="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Cancelar
                    </button>
                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                    >
                      {isSubmitting ? 'Salvando...' : mode === 'create' ? 'Criar Template' : 'Salvar Alterações'}
                    </button>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
} 